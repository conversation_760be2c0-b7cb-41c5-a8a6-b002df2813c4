import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { addFunds } from "./balance-service";
import { getTxLookup, initializeTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity } from "./types";
import { extractRawTonAddress } from "./utils";
import { getTonRpcUrl, getMarketplaceWallet } from "./config";
import { applyDepositFee } from "./fee-service";

async function findUserByTonWalletFlexible(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  const db = admin.firestore();
  const usersRef = db.collection("users");

  console.log(`Looking for user with TON address: ${tonWalletAddress}`);

  // First try exact match
  let query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
  let snapshot = await query.get();

  if (!snapshot.empty) {
    const doc = snapshot.docs[0];
    console.log(`Found exact match for address: ${tonWalletAddress}`);
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  }

  const rawAddress = extractRawTonAddress(tonWalletAddress);
  if (!rawAddress) {
    console.log(`Invalid address format: ${tonWalletAddress}`);
    return null;
  }

  console.log(
    `No exact match found, searching by raw address part: ${rawAddress}`
  );

  // Query directly by raw_ton_wallet_address field - much more efficient!
  const rawQuery = usersRef.where("raw_ton_wallet_address", "==", rawAddress);
  const rawSnapshot = await rawQuery.get();

  if (!rawSnapshot.empty) {
    const doc = rawSnapshot.docs[0];
    const userData = doc.data() as UserEntity;
    console.log(
      `Found user by raw address: ${userData.ton_wallet_address} matches ${tonWalletAddress}`
    );
    return {
      ...userData,
      id: doc.id,
    } as UserEntity;
  }

  console.log(`No user found for address: ${tonWalletAddress}`);
  return null;
}

function filterNewTransactions(
  transactions: TonTransaction[],
  lastCheckedLt: string
): TonTransaction[] {
  if (lastCheckedLt === "0") {
    console.log(
      "No previous transactions processed, returning all transactions"
    );
    return transactions;
  }

  const filtered = transactions.filter((tx) => {
    const txLt = parseInt(tx.transaction_id.lt);
    const lastLt = parseInt(lastCheckedLt);
    const isNew = txLt > lastLt;
    console.log(
      `Transaction LT: ${txLt}, Last checked LT: ${lastLt}, Is new: ${isNew}`
    );
    return isNew;
  });

  console.log(
    `Filtered ${transactions.length} transactions down to ${filtered.length} new ones`
  );
  return filtered;
}

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

interface QuickNodeTonResponse {
  ok: boolean;
  result: TonTransaction[];
}

async function fetchTonTransactions(
  address: string,
  fromLt?: string
): Promise<TonTransaction[]> {
  const baseUrl = getTonRpcUrl();

  // Build URL parameters for QuickNode TON API
  const params = new URLSearchParams({
    address: address,
    to_lt: "0",
    archival: "true",
  });

  // Add lt parameter if provided (for pagination) - but we're not using this anymore
  // We fetch all transactions and filter them in the application logic
  if (fromLt && fromLt !== "0") {
    params.append("lt", fromLt);
  }

  // TODO - also get only tx list when sum on ton in tx > 0.9 TON

  const url = `${baseUrl}getTransactions?${params.toString()}`;

  const headers: Record<string, string> = {
    accept: "application/json",
  };

  try {
    console.log(`Fetching TON transactions from: ${url}`);
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data: QuickNodeTonResponse = await response.json();

    if (!data.ok) {
      throw new Error("TON API returned error response");
    }

    console.log(`Fetched ${data.result?.length ?? 0} transactions`);
    return data.result ?? [];
  } catch (error) {
    console.error("Error fetching TON transactions:", error);
    throw error;
  }
}

function extractTransactionInfo(
  tx: TonTransaction
): { sender: string; amount: number; message?: string } | null {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = parseInt(tx.in_msg.value) / 1000000000;

  // Filter: only process transactions with amount > 0.9 TON
  if (amount <= 0.9) {
    console.log(
      `Transaction amount ${amount} TON is below 0.9 TON threshold, skipping`
    );
    return null;
  }

  const originalSender = tx.in_msg.source;

  console.log(
    `Transaction from address: ${originalSender}, amount: ${amount} TON`
  );

  return {
    sender: originalSender, // Keep original address, flexible matching will handle it
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function updateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  try {
    // Apply deposit fee and get net amount
    const netAmount = await applyDepositFee(userId, amount);

    await addFunds(userId, netAmount);
    console.log(
      `Updated balance for user ${userId}: +${netAmount} TON (after fees from ${amount} TON)`
    );
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

async function processTransactions(
  transactions: TonTransaction[]
): Promise<void> {
  console.log(`Processing ${transactions.length} transactions`);

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue;
      }

      console.log(
        `Processing transaction: ${tx.transaction_id.lt}, sender: ${txInfo.sender}, amount: ${txInfo.amount} TON`
      );

      const user = await findUserByTonWalletFlexible(txInfo.sender);

      if (!user) {
        console.log(`No user found for wallet address: ${txInfo.sender}`);
        continue;
      }

      // TODO fix that logic, when I update auth to telegram
      if (!user.tg_id) {
        console.log(`User ${user.id} has no tg_id, skipping balance update`);
        continue;
      }

      await updateUserBalance(user.id, txInfo.amount);

      console.log(
        `Successfully processed topup for user ${user.id} (${user.tg_id}): ${txInfo.amount} TON`
      );
    } catch (error) {
      console.error(
        `Error processing transaction ${tx.transaction_id.lt}:`,
        error
      );
    }
  }
}

export async function monitorTonTransactions(): Promise<void> {
  try {
    console.log("Starting TON transaction monitoring...");

    await initializeTxLookup();

    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id ?? "0";

    console.log(`Last checked LT: ${lastCheckedLt}`);

    const marketplaceWallet = getMarketplaceWallet();

    // Fetch all transactions (don't use fromLt parameter to get all available)
    const allTransactions = await fetchTonTransactions(marketplaceWallet);

    if (allTransactions.length === 0) {
      console.log("No transactions found");
      return;
    }

    // Filter out already processed transactions
    const newTransactions = filterNewTransactions(
      allTransactions,
      lastCheckedLt
    );

    if (newTransactions.length === 0) {
      console.log("No new transactions to process");
      return;
    }

    console.log(`Found ${newTransactions.length} new transactions to process`);

    // Sort transactions by LT (oldest first)
    newTransactions.sort(
      (a, b) => parseInt(a.transaction_id.lt) - parseInt(b.transaction_id.lt)
    );

    await processTransactions(newTransactions);

    if (newTransactions.length > 0) {
      const latestLt =
        newTransactions[newTransactions.length - 1].transaction_id.lt;
      await updateTxLookup(latestLt);
      console.log(`Updated last checked LT to: ${latestLt}`);
    }

    console.log("TON transaction monitoring completed successfully");
  } catch (error) {
    console.error("Error in TON transaction monitoring:", error);
    throw error;
  }
}
